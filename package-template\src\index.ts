/**
 * React Native Scaling Drawer
 * 
 * A beautiful, performant drawer navigation with scaling animations and shadow effects.
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

// Main components
export { ScalingDrawer } from './components/ScalingDrawer';

// Hooks
export { useScalingDrawer } from './hooks/useScalingDrawer';

// Types
export type {
  ScalingDrawerConfig,
  ScalingDrawerState,
  ScalingDrawerProps,
  DrawerMenuItemProps,
  DrawerHeaderProps,
  EasingType,
  DrawerPosition,
  ShadowConfig,
  AdvancedDrawerConfig,
} from './types';

// Default export for convenience
export { ScalingDrawer as default } from './components/ScalingDrawer';
