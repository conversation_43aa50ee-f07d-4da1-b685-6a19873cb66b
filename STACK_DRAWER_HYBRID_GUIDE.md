# Stack + Custom Drawer Hybrid Implementation Guide

## 🎯 What This Solves

The original custom drawer was beautiful but lacked:
- ❌ Deep linking support
- ❌ Navigation state persistence
- ❌ Proper back button handling
- ❌ URL routing

This hybrid solution provides:
- ✅ **React Navigation Stack** for proper navigation patterns
- ✅ **Custom Scaling Drawer** for visual appeal
- ✅ **Deep Linking** support with URL routing
- ✅ **TypeScript** support with full type safety
- ✅ **Performance** optimizations

## 📁 File Structure

```
src/
├── types/
│   └── navigation.ts          # Navigation types
├── hooks/
│   └── useDrawer.ts           # Drawer context & animations
├── navigation/
│   ├── StackNavigator.tsx     # Stack navigator with all screens
│   ├── NavigationContainer.tsx # Main container with drawer overlay
│   └── linking.ts             # Deep linking configuration
├── screens/
│   ├── HomeScreen.tsx
│   ├── ProfileScreen.tsx
│   ├── SettingsScreen.tsx
│   ├── AboutScreen.tsx
│   └── [YourScreens].tsx
```

## 🚀 Quick Implementation Steps

### 1. Install Dependencies

```bash
npm install @react-navigation/native @react-navigation/stack
npm install react-native-gesture-handler react-native-reanimated react-native-safe-area-context react-native-screens
```

### 2. Copy Core Files

Copy these files from the project:
- `src/types/navigation.ts`
- `src/hooks/useDrawer.ts` 
- `src/navigation/StackNavigator.tsx`
- `src/navigation/NavigationContainer.tsx`
- `src/navigation/linking.ts`

### 3. Update App.tsx

```tsx
import React from 'react';
import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { DrawerProvider } from './src/hooks/useDrawer';
import NavigationContainer from './src/navigation/NavigationContainer';

function App(): React.JSX.Element {
  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" />
      <DrawerProvider>
        <NavigationContainer />
      </DrawerProvider>
    </SafeAreaProvider>
  );
}

export default App;
```

### 4. Customize for Your Project

#### A. Update Navigation Types

In `src/types/navigation.ts`:

```tsx
export type RootStackParamList = {
  Dashboard: undefined;
  Settings: undefined;
  Profile: undefined;
  Orders: { userId: string }; // Example with params
  // Add your screens here
};
```

#### B. Update Stack Navigator

In `src/navigation/StackNavigator.tsx`:

```tsx
import DashboardScreen from '../screens/DashboardScreen';
import SettingsScreen from '../screens/SettingsScreen';
// Import your screens

<Stack.Screen name="Dashboard" component={DashboardScreen} />
<Stack.Screen name="Settings" component={SettingsScreen} />
// Add your screens
```

#### C. Update Drawer Menu

In `src/navigation/NavigationContainer.tsx`, find `CustomDrawerContent`:

```tsx
<TouchableOpacity onPress={() => navigateToScreen('Dashboard')}>
  <Text style={styles.menuItemText}>Dashboard</Text>
</TouchableOpacity>
// Add your menu items
```

#### D. Update Deep Linking

In `src/navigation/linking.ts`:

```tsx
config: {
  screens: {
    Dashboard: 'dashboard',
    Settings: 'settings',
    Orders: 'orders/:userId', // With parameters
    // Add your routes
  },
},
```

## 🎨 Customization Options

### Colors
```tsx
// In NavigationContainer.tsx styles
backgroundColor: '#YOUR_COLOR', // Drawer background
```

### Animation Speed
```tsx
// In useDrawer.ts
duration: 300, // milliseconds
```

### Scale Factor
```tsx
// In useDrawer.ts
toValue: 0.85, // 85% of original size
```

### Slide Distance
```tsx
// In useDrawer.ts
toValue: screenWidth * 0.8, // 80% of screen width
```

## 🔗 Deep Linking Examples

With this implementation, these URLs will work:

```
awesomeproject://home
awesomeproject://settings
awesomeproject://profile
awesomeproject://orders/123
```

## 🧪 Testing Deep Links

### iOS Simulator
```bash
xcrun simctl openurl booted "awesomeproject://settings"
```

### Android Emulator
```bash
adb shell am start -W -a android.intent.action.VIEW -d "awesomeproject://settings" com.yourapp
```

## 📱 Navigation Usage in Screens

```tsx
import { useNavigation } from '@react-navigation/native';
import { NavigationProp } from '../types/navigation';

const MyScreen = () => {
  const navigation = useNavigation<NavigationProp>();
  
  const goToSettings = () => {
    navigation.navigate('Settings');
  };
  
  const goToOrderWithId = () => {
    navigation.navigate('Orders', { userId: '123' });
  };
};
```

## 🎯 Benefits of This Approach

1. **Proper Navigation**: Full React Navigation support
2. **Deep Linking**: URL routing works out of the box
3. **Visual Appeal**: Custom scaling drawer with shadows
4. **Performance**: Optimized animations and rendering
5. **Type Safety**: Full TypeScript support
6. **Maintainable**: Clean separation of concerns
7. **Scalable**: Easy to add new screens and routes

## 🔧 Troubleshooting

### Common Issues:

1. **Drawer not opening**: Check if `DrawerProvider` wraps your app
2. **Navigation not working**: Verify screen names match in types and navigator
3. **Deep links not working**: Check linking configuration and URL schemes
4. **TypeScript errors**: Ensure all screen names are in `RootStackParamList`

### Performance Tips:

1. Use `React.memo` for heavy screens
2. Implement lazy loading for large screens
3. Use `useCallback` for navigation functions
4. Optimize images and assets

## 🚀 Next Steps

1. Add gesture support for swipe-to-open drawer
2. Implement state persistence
3. Add custom transitions between screens
4. Create reusable navigation components
5. Add analytics tracking for navigation events
