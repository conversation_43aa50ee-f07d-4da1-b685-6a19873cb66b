import React from 'react';
import { NavigationContainer as RNNavigationContainer } from '@react-navigation/native';
import { View, StyleSheet, Animated, TouchableOpacity, SafeAreaView, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import StackNavigator from './StackNavigator';
import { useDrawer } from '../hooks/useDrawer';
import { NavigationProp, ScreenName } from '../types/navigation';
import { linking } from './linking';

// Custom header component that integrates with navigation
const CustomHeader = () => {
  const navigation = useNavigation<NavigationProp>();
  const { openDrawer } = useDrawer();
  const [currentRoute, setCurrentRoute] = React.useState<string>('Home');

  React.useEffect(() => {
    const unsubscribe = navigation.addListener('state', (e) => {
      const state = e.data.state;
      if (state) {
        const routeName = state.routes[state.index]?.name;
        setCurrentRoute(routeName || 'Home');
      }
    });

    return unsubscribe;
  }, [navigation]);

  return (
    <View style={styles.header}>
      <TouchableOpacity style={styles.menuButton} onPress={openDrawer}>
        <View style={styles.burgerIcon}>
          <View style={styles.burgerLine} />
          <View style={styles.burgerLine} />
          <View style={styles.burgerLine} />
        </View>
      </TouchableOpacity>
      <Text style={styles.headerTitle}>{currentRoute}</Text>
      <View style={styles.rightPlaceholder} />
    </View>
  );
};

// Custom drawer content that integrates with navigation
const CustomDrawerContent = () => {
  const navigation = useNavigation<NavigationProp>();
  const { closeDrawer } = useDrawer();

  const navigateToScreen = (screenName: ScreenName) => {
    navigation.navigate(screenName);
    closeDrawer();
  };

  return (
    <View style={styles.drawerBackground}>
      <SafeAreaView style={styles.drawerContent}>
        {/* Profile Section */}
        <View style={styles.profileContainer}>
          <View style={styles.profileImageContainer}>
            <View style={styles.profileImage} />
          </View>
        </View>

        {/* Menu Items */}
        <View style={styles.menuContainer}>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('Home')}
          >
            <Text style={styles.menuItemText}>Home</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('Profile')}
          >
            <Text style={styles.menuItemText}>Profile</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('Heavy')}
          >
            <Text style={styles.menuItemText}>Heavy Screen</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('Settings')}
          >
            <Text style={styles.menuItemText}>Settings</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('About')}
          >
            <Text style={styles.menuItemText}>About</Text>
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>@89viral1</Text>
          <TouchableOpacity style={styles.subscribeButton}>
            <Text style={styles.subscribeButtonText}>Subscribe</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </View>
  );
};

// Main navigation container with custom drawer overlay
const NavigationContainer = () => {
  const { isDrawerOpen, slideAnim, scaleAnim, shadowOpacityAnim, closeDrawer } = useDrawer();

  return (
    <View style={styles.container}>
      {/* Drawer Background - Always rendered */}
      <CustomDrawerContent />

      {/* Shadow Layer - Animated with the screen */}
      <Animated.View
        style={[
          styles.shadowWrapper,
          {
            transform: [
              { translateX: slideAnim },
              { scale: scaleAnim },
            ],
            opacity: shadowOpacityAnim,
          },
        ]}
      >
        <View style={styles.shadowContainer}>
          {/* Multiple shadow layers for depth */}
          <View style={styles.shadowLayer1} />
          <View style={styles.shadowLayer2} />
          <View style={styles.shadowLayer3} />
          {/* Glow effect */}
          <View style={styles.glowLayer} />
        </View>
      </Animated.View>

      {/* Main Navigation Container - Animated Wrapper */}
      <Animated.View
        style={[
          styles.animatedWrapper,
          {
            transform: [
              { translateX: slideAnim },
              { scale: scaleAnim },
            ],
          },
        ]}
      >
        {/* Static Content Container - Prevents re-renders */}
        <View
          style={[
            styles.mainContainer,
            // eslint-disable-next-line react-native/no-inline-styles
            {
              borderRadius: isDrawerOpen ? 20 : 0,
              // Add subtle shadow to the screen itself
              shadowColor: '#000',
              shadowOffset: {
                width: isDrawerOpen ? -8 : 0,
                height: isDrawerOpen ? 8 : 0,
              },
              shadowOpacity: isDrawerOpen ? 0.3 : 0,
              shadowRadius: isDrawerOpen ? 15 : 0,
              elevation: isDrawerOpen ? 10 : 0,
            },
          ]}
        >
          {/* React Navigation Container */}
          <RNNavigationContainer linking={linking}>
            {/* Custom Header */}
            <CustomHeader />

            {/* Stack Navigator */}
            <View style={styles.navigatorContainer}>
              <StackNavigator />
            </View>
          </RNNavigationContainer>

          {/* Invisible overlay that captures all touches when drawer is open */}
          {isDrawerOpen && (
            <TouchableOpacity
              style={styles.invisibleOverlay}
              onPress={closeDrawer}
              activeOpacity={1}
            />
          )}
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FF0000', // Red background for drawer
  },
  // Drawer styles
  drawerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#FF0000',
    zIndex: 1,
  },
  drawerContent: {
    flex: 1,
    paddingTop: 20,
  },
  profileContainer: {
    alignItems: 'center',
    marginBottom: 30,
    paddingTop: 20,
  },
  profileImageContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#CCCCCC',
  },
  menuContainer: {
    flex: 1,
    paddingTop: 10,
  },
  menuItem: {
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  menuItemText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.2)',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  footerText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  subscribeButton: {
    backgroundColor: '#000000',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
  },
  subscribeButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
  },

  // Animation and layout styles
  shadowWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
  },
  shadowContainer: {
    flex: 1,
    position: 'relative',
  },
  shadowLayer1: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 20,
    transform: [{ translateX: -5 }, { translateY: 5 }],
  },
  shadowLayer2: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.08)',
    borderRadius: 20,
    transform: [{ translateX: -10 }, { translateY: 10 }],
  },
  shadowLayer3: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.06)',
    borderRadius: 20,
    transform: [{ translateX: -15 }, { translateY: 15 }],
  },
  glowLayer: {
    position: 'absolute',
    top: -10,
    left: -10,
    right: -10,
    bottom: -10,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 30,
  },
  animatedWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 3,
  },
  mainContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
  },
  navigatorContainer: {
    flex: 1,
  },

  // Header styles
  header: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    zIndex: 1000,
  },
  menuButton: {
    padding: 10,
  },
  burgerIcon: {
    width: 24,
    height: 18,
    justifyContent: 'space-between',
  },
  burgerLine: {
    width: '100%',
    height: 2,
    backgroundColor: '#000000',
    borderRadius: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  rightPlaceholder: {
    width: 44,
  },

  // Invisible overlay that captures touches when drawer is open
  invisibleOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 999,
  },
});

export default NavigationContainer;
