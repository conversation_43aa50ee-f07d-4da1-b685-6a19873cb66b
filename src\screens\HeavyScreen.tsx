import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator } from 'react-native';

// Simulated heavy screen with API calls and complex state
const HeavyScreen: React.FC = React.memo(() => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [renderCount, setRenderCount] = useState(0);

  // Simulate heavy API call
  useEffect(() => {
    console.log('🔥 HeavyScreen: API call triggered');
    
    const fetchData = async () => {
      setLoading(true);
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate heavy data processing
      const heavyData = Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        title: `Item ${i}`,
        description: `Description for item ${i}`,
        timestamp: new Date().toISOString(),
      }));
      
      setData(heavyData);
      setLoading(false);
    };

    fetchData();
  }, []); // Only run once when component mounts

  // Track re-renders
  useEffect(() => {
    setRenderCount(prev => prev + 1);
    console.log(`🔄 HeavyScreen re-rendered ${renderCount + 1} times`);
  });

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF0000" />
        <Text style={styles.loadingText}>Loading heavy data...</Text>
        <Text style={styles.renderCount}>Render count: {renderCount}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Heavy Screen</Text>
      <Text style={styles.renderCount}>Render count: {renderCount}</Text>
      <Text style={styles.description}>
        This screen simulates heavy API calls and complex data processing.
        Notice how it doesn't re-render when the drawer opens/closes!
      </Text>
      
      <ScrollView style={styles.scrollView}>
        {data.slice(0, 50).map((item) => (
          <View key={item.id} style={styles.item}>
            <Text style={styles.itemTitle}>{item.title}</Text>
            <Text style={styles.itemDescription}>{item.description}</Text>
          </View>
        ))}
      </ScrollView>
    </View>
  );
});

HeavyScreen.displayName = 'HeavyScreen';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#000000',
  },
  renderCount: {
    fontSize: 14,
    color: '#FF0000',
    fontWeight: 'bold',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    color: '#333333',
    marginBottom: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333333',
  },
  scrollView: {
    flex: 1,
  },
  item: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000000',
  },
  itemDescription: {
    fontSize: 14,
    color: '#666666',
    marginTop: 5,
  },
});

export default HeavyScreen;
