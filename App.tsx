/**
 * React Native App with Custom Drawer + Stack Navigation
 * Supports deep linking and proper navigation patterns
 *
 * @format
 */

import React from 'react';
import { StatusBar, useColorScheme } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { DrawerProvider } from './src/hooks/useDrawer';
import NavigationContainer from './src/navigation/NavigationContainer';

function App(): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';

  return (
    <SafeAreaProvider>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={isDarkMode ? '#000000' : '#FFFFFF'}
      />
      <DrawerProvider>
        <NavigationContainer />
      </DrawerProvider>
    </SafeAreaProvider>
  );
}

export default App;
