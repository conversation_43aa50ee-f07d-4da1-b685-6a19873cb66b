import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

type ProfileScreenProps = {
  // Add any props if needed
};

const ProfileScreen: React.FC<ProfileScreenProps> = React.memo(() => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Profile Screen</Text>
      <Text style={styles.description}>
        This is the profile screen of the application.
      </Text>
    </View>
  );
});

ProfileScreen.displayName = 'ProfileScreen';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#000000',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    color: '#333333',
  },
});

export default ProfileScreen;
