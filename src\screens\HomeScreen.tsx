import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

type HomeScreenProps = {
  // Add any props if needed
};

const HomeScreen: React.FC<HomeScreenProps> = React.memo(() => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Home Screen</Text>
      <Text style={styles.description}>
        This is the main screen of the application. Click the burger icon to open the drawer.
      </Text>
    </View>
  );
});

HomeScreen.displayName = 'HomeScreen';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#000000',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    color: '#333333',
  },
});

export default HomeScreen;
