import { LinkingOptions } from '@react-navigation/native';
import { RootStackParamList } from '../types/navigation';

// Deep linking configuration
export const linking: LinkingOptions<RootStackParamList> = {
  prefixes: [
    'awesomeproject://', // Custom URL scheme
    'https://awesomeproject.com', // Universal links (if you have a website)
  ],
  config: {
    screens: {
      Home: 'home',
      Profile: 'profile',
      Heavy: 'heavy',
    },
  },
};
