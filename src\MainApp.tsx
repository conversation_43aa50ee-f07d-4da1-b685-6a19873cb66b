/* eslint-disable react-native/no-inline-styles */
import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  SafeAreaView,
} from 'react-native';
import ScreenRenderer from './components/ScreenRenderer';
import { useDrawer } from './hooks/useDrawer';

type Screen = 'Home' | 'Profile' | 'Heavy';

const MainApp: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState<Screen>('Home');
  const { isDrawerOpen, slideAnim, scaleAnim, shadowOpacityAnim, openDrawer, closeDrawer } = useDrawer();

  const navigateToScreen = (screen: Screen) => {
    setCurrentScreen(screen);
    closeDrawer();
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity style={styles.menuButton} onPress={openDrawer}>
        <View style={styles.burgerIcon}>
          <View style={styles.burgerLine} />
          <View style={styles.burgerLine} />
          <View style={styles.burgerLine} />
        </View>
      </TouchableOpacity>
      <Text style={styles.headerTitle}>{currentScreen}</Text>
      <View style={styles.rightPlaceholder} />
    </View>
  );

  const renderDrawer = () => (
    <View style={styles.drawerBackground}>
      <SafeAreaView style={styles.drawerContent}>
        {/* Profile Section */}
        <View style={styles.profileContainer}>
          <View style={styles.profileImageContainer}>
            <View style={styles.profileImage} />
          </View>
        </View>

        {/* Menu Items */}
        <View style={styles.menuContainer}>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('Home')}
          >
            <Text style={styles.menuItemText}>Home</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('Profile')}
          >
            <Text style={styles.menuItemText}>Profile</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('Heavy')}
          >
            <Text style={styles.menuItemText}>Heavy Screen</Text>
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>@89viral1</Text>
          <TouchableOpacity style={styles.subscribeButton}>
            <Text style={styles.subscribeButtonText}>Subscribe</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </View>
  );

  // Optimized screen renderer that prevents unnecessary re-renders
  const screenContent = React.useMemo(() => {
    return <ScreenRenderer currentScreen={currentScreen} />;
  }, [currentScreen]);

  return (
    <View style={styles.container}>
      {/* Drawer Background - Always rendered */}
      {renderDrawer()}

      {/* Shadow Layer - Animated with the screen */}
      <Animated.View
        style={[
          styles.shadowWrapper,
          {
            transform: [
              { translateX: slideAnim },
              { scale: scaleAnim },
            ],
            opacity: shadowOpacityAnim,
          },
        ]}
      >
        <View style={styles.shadowContainer}>
          {/* Multiple shadow layers for depth */}
          <View style={styles.shadowLayer1} />
          <View style={styles.shadowLayer2} />
          <View style={styles.shadowLayer3} />
          {/* Glow effect */}
          <View style={styles.glowLayer} />
        </View>
      </Animated.View>

      {/* Main Screen Container - Animated Wrapper */}
      <Animated.View
        style={[
          styles.animatedWrapper,
          {
            transform: [
              { translateX: slideAnim },
              { scale: scaleAnim },
            ],
          },
        ]}
      >
        {/* Static Content Container - Prevents re-renders */}
        <View
          style={[
            styles.mainContainer,
            {
              borderRadius: isDrawerOpen ? 20 : 0,
              // Add subtle shadow to the screen itself
              shadowColor: '#000',
              shadowOffset: {
                width: isDrawerOpen ? -8 : 0,
                height: isDrawerOpen ? 8 : 0,
              },
              shadowOpacity: isDrawerOpen ? 0.3 : 0,
              shadowRadius: isDrawerOpen ? 15 : 0,
              elevation: isDrawerOpen ? 10 : 0,
            },
          ]}
        >
          {/* Always render the header normally */}
          {renderHeader()}

          {/* Screen content */}
          <View style={styles.screenContainer}>
            {screenContent}
          </View>

          {/* Invisible overlay that captures all touches when drawer is open - covers entire main container */}
          {isDrawerOpen && (
            <TouchableOpacity
              style={styles.invisibleOverlay}
              onPress={closeDrawer}
              activeOpacity={1}
            />
          )}
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FF0000', // Red background for drawer
  },
  // Drawer styles
  drawerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#FF0000',
    zIndex: 1,
  },
  drawerContent: {
    flex: 1,
    paddingTop: 20,
  },
  profileContainer: {
    alignItems: 'center',
    marginBottom: 30,
    paddingTop: 20,
  },
  profileImageContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#CCCCCC',
  },
  menuContainer: {
    flex: 1,
    paddingTop: 10,
  },
  menuItem: {
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  menuItemText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.2)',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  footerText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  subscribeButton: {
    backgroundColor: '#000000',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
  },
  subscribeButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
  },
  // Shadow wrapper - positioned behind the main screen
  shadowWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2, // Between drawer and main screen
  },
  shadowContainer: {
    flex: 1,
    position: 'relative',
  },
  // Multiple shadow layers for realistic depth effect
  shadowLayer1: {
    position: 'absolute',
    top: 25,
    left: 25,
    right: -25,
    bottom: -25,
    backgroundColor: 'rgba(0, 0, 0, 0.15)',
    borderRadius: 25,
  },
  shadowLayer2: {
    position: 'absolute',
    top: 15,
    left: 15,
    right: -15,
    bottom: -15,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 20,
  },
  shadowLayer3: {
    position: 'absolute',
    top: 8,
    left: 8,
    right: -8,
    bottom: -8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 15,
  },
  // Subtle glow effect around the screen
  glowLayer: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: 2,
    bottom: 2,
    backgroundColor: 'transparent',
    borderRadius: 22,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#FF0000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  // Animation wrapper - only handles transforms
  animatedWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 3,
  },
  // Main screen container styles - static content
  mainContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
  },
  screenOverlay: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
  },
  // Invisible overlay that captures touches when drawer is open
  invisibleOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 999,
  },
  header: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  menuButton: {
    padding: 10,
  },
  burgerIcon: {
    width: 24,
    height: 18,
    justifyContent: 'space-between',
  },
  burgerLine: {
    width: '100%',
    height: 2,
    backgroundColor: '#000000',
    borderRadius: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  rightPlaceholder: {
    width: 44,
  },
});

export default MainApp;
