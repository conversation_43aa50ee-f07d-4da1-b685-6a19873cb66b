{"name": "react-native-scaling-drawer", "version": "1.0.0", "description": "A beautiful, performant drawer navigation with scaling animations and shadow effects for React Native", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "tsc", "prepare": "npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "typecheck": "tsc --noEmit", "example": "cd example && npm start", "docs": "typedoc src/index.ts"}, "keywords": ["react-native", "drawer", "navigation", "animation", "scaling", "sidebar", "menu", "mobile", "ui", "component", "typescript"], "author": {"name": "89viral1", "url": "https://github.com/89viral1"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/89viral1/react-native-scaling-drawer.git"}, "bugs": {"url": "https://github.com/89viral1/react-native-scaling-drawer/issues"}, "homepage": "https://github.com/89viral1/react-native-scaling-drawer#readme", "peerDependencies": {"react": ">=16.8.0", "react-native": ">=0.60.0"}, "devDependencies": {"@react-native-community/eslint-config": "^3.2.0", "@testing-library/react-native": "^12.0.0", "@types/jest": "^29.5.0", "@types/react": "^18.0.0", "@types/react-native": "^0.72.0", "eslint": "^8.40.0", "jest": "^29.5.0", "react": "^18.2.0", "react-native": "^0.72.0", "react-test-renderer": "^18.2.0", "typedoc": "^0.24.0", "typescript": "^5.0.0"}, "files": ["lib", "src", "README.md", "LICENSE", "CHANGELOG.md"], "publishConfig": {"access": "public"}, "engines": {"node": ">=14.0.0"}, "jest": {"preset": "react-native", "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/types/**/*"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "eslintConfig": {"extends": "@react-native-community", "rules": {"prettier/prettier": "off"}}}