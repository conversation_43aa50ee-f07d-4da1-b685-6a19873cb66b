# Quick Implementation Template

## Files to Copy (Exact Order)

### 1. Copy useDrawer.ts
```bash
# Copy from: src/hooks/useDrawer.ts
# To: src/hooks/useDrawer.ts
```

### 2. Copy ScreenRenderer.tsx
```bash
# Copy from: src/components/ScreenRenderer.tsx  
# To: src/components/ScreenRenderer.tsx
```

### 3. Create Your Screens
Create your app-specific screens in `src/screens/`:

```tsx
// Example: src/screens/DashboardScreen.tsx
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const DashboardScreen = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Dashboard</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  title: { fontSize: 24, fontWeight: 'bold' },
});

export default DashboardScreen;
```

### 4. Modify MainApp.tsx Template

```tsx
import React, { useState } from 'react';
import {
  View, Text, StyleSheet, TouchableOpacity, Animated, SafeAreaView,
} from 'react-native';
import ScreenRenderer from './components/ScreenRenderer';
import { useDrawer } from './hooks/useDrawer';

// 🔥 CHANGE THIS: Define your screen types
type Screen = 'Dashboard' | 'Settings' | 'Profile';

const MainApp: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState<Screen>('Dashboard');
  const { isDrawerOpen, slideAnim, scaleAnim, shadowOpacityAnim, openDrawer, closeDrawer } = useDrawer();

  const navigateToScreen = (screen: Screen) => {
    setCurrentScreen(screen);
    closeDrawer();
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity style={styles.menuButton} onPress={openDrawer}>
        <View style={styles.burgerIcon}>
          <View style={styles.burgerLine} />
          <View style={styles.burgerLine} />
          <View style={styles.burgerLine} />
        </View>
      </TouchableOpacity>
      <Text style={styles.headerTitle}>{currentScreen}</Text>
      <View style={styles.rightPlaceholder} />
    </View>
  );

  const renderDrawer = () => (
    <View style={styles.drawerBackground}>
      <SafeAreaView style={styles.drawerContent}>
        {/* 🔥 CHANGE THIS: Your menu items */}
        <View style={styles.menuContainer}>
          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('Dashboard')}
          >
            <Text style={styles.menuItemText}>Dashboard</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('Settings')}
          >
            <Text style={styles.menuItemText}>Settings</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.menuItem}
            onPress={() => navigateToScreen('Profile')}
          >
            <Text style={styles.menuItemText}>Profile</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </View>
  );

  const screenContent = React.useMemo(() => {
    return <ScreenRenderer currentScreen={currentScreen} />;
  }, [currentScreen]);

  return (
    <View style={styles.container}>
      {renderDrawer()}

      {/* Shadow Layer */}
      <Animated.View
        style={[
          styles.shadowWrapper,
          {
            transform: [{ translateX: slideAnim }, { scale: scaleAnim }],
            opacity: shadowOpacityAnim,
          },
        ]}
      >
        <View style={styles.shadowContainer}>
          <View style={styles.shadowLayer1} />
          <View style={styles.shadowLayer2} />
          <View style={styles.shadowLayer3} />
          <View style={styles.glowLayer} />
        </View>
      </Animated.View>

      {/* Main Screen */}
      <Animated.View
        style={[
          styles.animatedWrapper,
          {
            transform: [{ translateX: slideAnim }, { scale: scaleAnim }],
          },
        ]}
      >
        <View
          style={[
            styles.mainContainer,
            {
              borderRadius: isDrawerOpen ? 20 : 0,
              shadowColor: '#000',
              shadowOffset: {
                width: isDrawerOpen ? -8 : 0,
                height: isDrawerOpen ? 8 : 0,
              },
              shadowOpacity: isDrawerOpen ? 0.3 : 0,
              shadowRadius: isDrawerOpen ? 15 : 0,
              elevation: isDrawerOpen ? 10 : 0,
            },
          ]}
        >
          {renderHeader()}
          <View style={styles.screenContainer}>
            {screenContent}
          </View>
          {isDrawerOpen && (
            <TouchableOpacity
              style={styles.invisibleOverlay}
              onPress={closeDrawer}
              activeOpacity={1}
            />
          )}
        </View>
      </Animated.View>
    </View>
  );
};

// 🔥 CHANGE THIS: Customize colors
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FF0000', // Your drawer background color
  },
  drawerBackground: {
    position: 'absolute',
    top: 0, left: 0, right: 0, bottom: 0,
    backgroundColor: '#FF0000', // Match container color
    zIndex: 1,
  },
  drawerContent: { flex: 1, paddingTop: 20 },
  menuContainer: { flex: 1, paddingTop: 50 },
  menuItem: { paddingVertical: 15, paddingHorizontal: 20 },
  menuItemText: { fontSize: 16, color: '#FFFFFF', fontWeight: '500' },
  
  // Copy all other styles from original MainApp.tsx
  // ... (shadow styles, header styles, etc.)
});

export default MainApp;
```

### 5. Update ScreenRenderer.tsx

```tsx
// In ScreenRenderer.tsx, update the screens object:
const screens = useMemo(() => ({
  Dashboard: <DashboardScreen />,
  Settings: <SettingsScreen />,
  Profile: <ProfileScreen />,
  // Add your screens here
}), []);
```

### 6. Update App.tsx

```tsx
import React from 'react';
import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import MainApp from './src/MainApp';

function App(): React.JSX.Element {
  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" />
      <MainApp />
    </SafeAreaProvider>
  );
}

export default App;
```

## Quick Checklist

- [ ] Copy `useDrawer.ts` hook
- [ ] Copy `ScreenRenderer.tsx` component  
- [ ] Create your screen components
- [ ] Update screen types in MainApp.tsx
- [ ] Update menu items in renderDrawer()
- [ ] Update screens object in ScreenRenderer.tsx
- [ ] Customize colors in styles
- [ ] Update App.tsx to use MainApp
- [ ] Test drawer opening/closing
- [ ] Test screen navigation
- [ ] Test on both iOS and Android

## Common Customizations

### Change Drawer Color
```tsx
backgroundColor: '#YOUR_HEX_COLOR'
```

### Change Animation Speed
```tsx
duration: 300 // milliseconds
```

### Change Scale Factor
```tsx
toValue: 0.85 // 85% of original size
```

### Change Slide Distance
```tsx
toValue: screenWidth * 0.8 // 80% of screen width
```
